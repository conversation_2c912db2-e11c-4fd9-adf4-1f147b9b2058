import { Checkbox } from "@heroui/react";
import { Fragment } from "react";

const PermissionsSection = ({
  permissions,
  selectedPermissions,
  onSelectAll,
  onSinglePermission,
  onFullAccess,
  isSelectAllChecked,
  isPermissionChecked,
  isFullAccessChecked,
}) => {
  return (
    <div className="flex flex-col gap-4">
      <p className="font-medium mt-2">دسترسی‌ها</p>
      
      {/* چک‌باکس "دسترسی کامل" - زمانی که کلیک شود همه permissions در تمام گروه‌ها را انتخاب/لغو انتخاب می‌کند */}
      <Checkbox
        value={"all"}
        key={"all"}
        size="lg"
        radius="full"
        color="primary"
        isSelected={isFullAccessChecked()}
        onChange={onFullAccess}
        classNames={{
          label: "text-sm font-medium text-inherit",
          base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
        }}
      >
        دسترسی کامل
      </Checkbox>

      {permissions?.map((item) => {
        return (
          <Fragment key={item?.name}>
            <p className="font-medium mt-2">{item?.display_name}</p>
            <div className="flex items-center gap-3">
              {/* چک‌باکس "انتخاب همه" - زمانی که کلیک شود همه permissions این گروه را انتخاب/لغو انتخاب می‌کند */}
              <Checkbox
                value={item?.name}
                key={item?.name}
                size="lg"
                radius="full"
                color="primary"
                isSelected={isSelectAllChecked(item?.permissions)}
                onChange={() => onSelectAll(item?.name, item?.permissions)}
                classNames={{
                  label: "text-sm font-medium text-inherit",
                  base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                }}
              >
                انتخاب همه
              </Checkbox>
              
              {/* چک‌باکس‌های تک permission - رفتار radio دارند (فقط یکی در هر گروه قابل انتخاب) */}
              {item?.permissions?.map((perm) => {
                return (
                  <Checkbox
                    value={perm?.name}
                    key={perm?.name}
                    size="lg"
                    radius="full"
                    color="primary"
                    isSelected={isPermissionChecked(perm?.name)}
                    onChange={() =>
                      onSinglePermission(perm?.name, item?.permissions)
                    }
                    classNames={{
                      label: "text-sm font-medium text-inherit",
                      base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                    }}
                  >
                    {perm?.display_name}
                  </Checkbox>
                );
              })}
            </div>
          </Fragment>
        );
      })}
    </div>
  );
};

export default PermissionsSection;
