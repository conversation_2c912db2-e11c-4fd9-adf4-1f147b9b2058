import { Button } from "@heroui/react";

const ActionButtons = ({ onCancel, onSubmit }) => {
  return (
    <div className="flex gap-3 justify-end items-center">
      <Button
        type="button"
        radius="full"
        className="md:max-w-52"
        fullWidth
        size="lg"
        onPress={onCancel}
      >
        انصراف
      </Button>
      <Button
        type="submit"
        color="primary"
        radius="full"
        className="md:max-w-52"
        fullWidth
        size="lg"
        onPress={onSubmit}
      >
        ثبت سازمان
      </Button>
    </div>
  );
};

export default ActionButtons;
