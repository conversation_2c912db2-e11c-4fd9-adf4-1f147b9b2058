import { addToast, useDisclosure } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { parseAsString, useQueryStates } from "nuqs";
import { useEffect, useMemo, useState } from "react";
import { Form, useForm } from "react-hook-form";
import z from "zod";
import api from "../../api";
import PageWrapper from "../../components/layout/PageWrapper";

// Import components
import ActionButtons from "./components/ActionButtons";
import CreateAdminModal from "./components/CreateAdminModal";
import OrganizationInfoForm from "./components/OrganizationInfoForm";
import PermissionsSection from "./components/PermissionsSection";
import RoleSection from "./components/RoleSection";
import UserSearchSection from "./components/UserSearchSection";

const schema = z.object({
  first_name: z.string().min(3, "نام باید حداقل 3 کاراکتر باشد"),
  last_name: z.string().min(3, "نام خانوادگی باید حداقل 3 کاراکتر باشد"),
  mobile: z
    .string()
    .regex(/^\d+$/, "لطفا فقط عدد وارد کنید")
    .min(11, "شماره تلفن باید حداقل ۱۱ رقم باشد")
    .max(11, "شماره تلفن باید دقیقاً ۱۱ رقم باشد")
    .regex(/^09[0-9]{9}$/, "شماره تلفن معتبر نیست"),
  password: z.string().min(8, "رمز عبور باید حداقل ۸ کاراکتر باشد"),
});

const CreateOrganizationsPage = () => {
  const [selectedUsers, setSelectedUsers] = useState([]);
  const MAX_SELECTED_USERS = 1;

  // State برای مدیریت انتخاب permissions
  const [selectedPermissions, setSelectedPermissions] = useState([]);

  // Disclosure for create modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  // Create admin Form
  const {
    control: createAdminControl,
    handleSubmit: createAdminSubmit,
    reset: createAdminReset,
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      first_name: "",
      last_name: "",
      mobile: "",
      password: "",
    },
  });

  const { mutate } = api.Entities.add.useMutation({
    onSuccess: (data) => {
      addToast({
        title: data?.message,
        variant: "solid",
        color: data?.status ? "success" : "danger",
      });
      createAdminReset();
      onClose();
    },
    onError: () => {
      addToast({
        title: "مشکلی پیش آمده است، مجددا تلاش کنید",
        color: "danger",
      });
    },
  });

  const { control, handleSubmit } = useForm({
    defaultValues: {},
  });

  const handleSelectUser = (user) => {
    if (MAX_SELECTED_USERS === 1) {
      setSelectedUsers([user]);
    } else {
      setSelectedUsers((prev) => {
        const isAlreadySelected = prev.some((u) => u.id === user.id);
        if (isAlreadySelected) {
          return prev.filter((u) => u.id !== user.id);
        }
        if (prev.length >= MAX_SELECTED_USERS) {
          return prev;
        }
        return [...prev, user];
      });
    }
  };

  const handleRemoveUser = (userId) => {
    setSelectedUsers((prev) => prev.filter((user) => user.id !== userId));
  };

  // تابع برای مدیریت انتخاب "انتخاب همه" برای هر گروه permissions
  const handleSelectAll = (groupName, groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);
    const isAllSelected = allPermissionNames.every((permName) =>
      selectedPermissions.includes(permName),
    );

    if (isAllSelected) {
      // اگر همه انتخاب شده‌اند، همه را از انتخاب خارج کن
      setSelectedPermissions((prev) =>
        prev.filter((perm) => !allPermissionNames.includes(perm)),
      );
    } else {
      // اگر همه انتخاب نشده‌اند، همه را انتخاب کن
      setSelectedPermissions((prev) => {
        const filtered = prev.filter(
          (perm) => !allPermissionNames.includes(perm),
        );
        return [...filtered, ...allPermissionNames];
      });
    }
  };

  // تابع برای مدیریت انتخاب تک permission (رفتار radio با قابلیت لغو انتخاب)
  const handleSinglePermission = (permissionName, groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);

    // بررسی اینکه آیا این permission قبلاً انتخاب شده است یا نه
    const isCurrentlySelected = selectedPermissions.includes(permissionName);

    // ابتدا همه permissions این گروه را از انتخاب خارج کن
    const filteredPermissions = selectedPermissions.filter(
      (perm) => !allPermissionNames.includes(perm),
    );

    if (isCurrentlySelected) {
      // اگر قبلاً انتخاب شده بود، فقط آن را از انتخاب خارج کن (هیچ چیز اضافه نکن)
      setSelectedPermissions(filteredPermissions);
    } else {
      // اگر انتخاب نشده بود، آن را اضافه کن
      setSelectedPermissions([...filteredPermissions, permissionName]);
    }
  };

  // تابع برای بررسی اینکه آیا "انتخاب همه" برای یک گروه فعال است یا نه
  const isSelectAllChecked = (groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);
    return allPermissionNames.every((permName) =>
      selectedPermissions.includes(permName),
    );
  };

  // تابع برای بررسی اینکه آیا یک permission خاص انتخاب شده است یا نه
  const isPermissionChecked = (permissionName) => {
    return selectedPermissions.includes(permissionName);
  };

  // تابع برای مدیریت "دسترسی کامل" - انتخاب/لغو انتخاب همه permissions
  const handleFullAccess = () => {
    if (!permissions) return;

    // تمام permissions موجود را استخراج کن
    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.name),
    );

    // بررسی اینکه آیا همه permissions انتخاب شده‌اند یا نه
    const areAllSelected = allPermissions.every((permName) =>
      selectedPermissions.includes(permName),
    );

    if (areAllSelected) {
      // اگر همه انتخاب شده‌اند، همه را از انتخاب خارج کن
      setSelectedPermissions([]);
    } else {
      // اگر همه انتخاب نشده‌اند، همه را انتخاب کن
      setSelectedPermissions(allPermissions);
    }
  };

  // تابع برای بررسی اینکه آیا "دسترسی کامل" فعال است یا نه
  const isFullAccessChecked = () => {
    if (!permissions) return false;

    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.name),
    );

    return (
      allPermissions.length > 0 &&
      allPermissions.every((permName) => selectedPermissions.includes(permName))
    );
  };

  const onSubmit = (data) => {
    // تبدیل آرایه permissions انتخاب شده به رشته با کاما
    const permissionsString = selectedPermissions.join(",");

    const formData = {
      ...data,
      entity_id:
        MAX_SELECTED_USERS === 1
          ? selectedUsers[0]?.id
          : selectedUsers.map((user) => user.id),
      permissions: permissionsString, // اضافه کردن permissions به formData
    };
    console.log(formData);
  };

  const [usernameQuery] = useQueryStates({
    username: parseAsString,
  });

  // Get Entities Data
  const {
    data: _data,
    isLoading,
    error,
  } = api.Entities.list.useQuery({
    enabled: !!usernameQuery.username,
    variables: {
      query: {
        username: usernameQuery?.username,
      },
    },
  });
  const users = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  // Get Permissions Data
  const { data: _permissions, error: permissionsError } =
    api.Permissions.list.useQuery();
  const permissions = useMemo(() => _permissions?.data, [_permissions]);

  useEffect(() => {
    if (_permissions?.status === false || permissionsError) {
      addToast({
        title: _permissions?.message
          ? _permissions?.message
          : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [permissionsError, _permissions]);

  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <OrganizationInfoForm control={control} />

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <UserSearchSection
            users={users}
            isLoading={isLoading}
            selectedUsers={selectedUsers}
            onSelectUser={handleSelectUser}
            onRemoveUser={handleRemoveUser}
            onOpenCreateModal={onOpen}
            MAX_SELECTED_USERS={MAX_SELECTED_USERS}
          />

          <CreateAdminModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            onClose={onClose}
            control={createAdminControl}
            onSubmit={createAdminSubmit(mutate)}
            onReset={createAdminReset}
          />

          <RoleSection control={control} />

          <PermissionsSection
            permissions={permissions}
            selectedPermissions={selectedPermissions}
            onSelectAll={handleSelectAll}
            onSinglePermission={handleSinglePermission}
            onFullAccess={handleFullAccess}
            isSelectAllChecked={isSelectAllChecked}
            isPermissionChecked={isPermissionChecked}
            isFullAccessChecked={isFullAccessChecked}
          />

          <ActionButtons
            onCancel={() => {}}
            onSubmit={handleSubmit(onSubmit)}
          />
        </div>
      </Form>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
