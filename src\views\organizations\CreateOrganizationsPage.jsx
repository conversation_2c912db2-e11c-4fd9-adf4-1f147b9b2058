import {
  Avatar,
  Button,
  Checkbox,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ow,
  <PERSON>kel<PERSON>,
  Spinner,
  addToast,
  useDisclosure,
} from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { AnimatePresence, motion } from "framer-motion";
import {
  Add,
  Call,
  Edit,
  Eye,
  EyeSlash,
  Lock1,
  Trash,
  UserSquare,
} from "iconsax-reactjs";
import { parseAsString, useQueryStates } from "nuqs";
import { Fragment, useEffect, useMemo, useState } from "react";
import { Form, useForm } from "react-hook-form";
import z from "zod";
import api from "../../api";

import FormDatePicker from "../../components/form/FormDatePicker";
import FormInput from "../../components/form/FormInput";

import FormCheckboxGroup from "../../components/form/FormCheckboxGroup";
import FormRichText from "../../components/form/FormRichText/FormRichText";
import FormSwitch from "../../components/form/FormSwitch";
import FormUpload from "../../components/form/FormUpload";
import FormUploadAvatar from "../../components/form/FormUploadAvatar";
import Icon from "../../components/icon/Icon";
import PageWrapper from "../../components/layout/PageWrapper";
import SearchFilter from "../../components/table/filters/SearchFilter";

const schema = z.object({
  first_name: z.string().min(3, "نام باید حداقل 3 کاراکتر باشد"),
  last_name: z.string().min(3, "نام خانوادگی باید حداقل 3 کاراکتر باشد"),
  mobile: z
    .string()
    .regex(/^\d+$/, "لطفا فقط عدد وارد کنید")
    .min(11, "شماره تلفن باید حداقل ۱۱ رقم باشد")
    .max(11, "شماره تلفن باید دقیقاً ۱۱ رقم باشد")
    .regex(/^09[0-9]{9}$/, "شماره تلفن معتبر نیست"),
  password: z.string().min(8, "رمز عبور باید حداقل ۸ کاراکتر باشد"),
});

const CreateOrganizationsPage = () => {
  const [searchFocused, setSearchFocused] = useState(false);

  const [selectedUsers, setSelectedUsers] = useState([]);

  const MAX_SELECTED_USERS = 1;

  // State برای مدیریت انتخاب permissions
  const [selectedPermissions, setSelectedPermissions] = useState([]);

  // Disclosure for create modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  // Create admin Form
  const {
    control: createAdminControl,
    handleSubmit: createAdminSubmit,
    reset: createAdminReset,
  } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      first_name: "",
      last_name: "",
      mobile: "",
      password: "",
    },
  });

  const { mutate } = api.Entities.add.useMutation({
    onSuccess: (data) => {
      addToast({
        title: data?.message,
        variant: "solid",
        color: data?.status ? "success" : "danger",
      });
      createAdminReset();
      onClose();
    },
    onError: () => {
      addToast({
        title: "مشکلی پیش آمده است، مجددا تلاش کنید",
        color: "danger",
      });
    },
  });

  const { control, handleSubmit } = useForm({
    defaultValues: {},
  });

  const handleSelectUser = (user) => {
    if (MAX_SELECTED_USERS === 1) {
      setSelectedUsers([user]);
    } else {
      setSelectedUsers((prev) => {
        const isAlreadySelected = prev.some((u) => u.id === user.id);
        if (isAlreadySelected) {
          return prev.filter((u) => u.id !== user.id);
        }
        if (prev.length >= MAX_SELECTED_USERS) {
          return prev;
        }
        return [...prev, user];
      });
    }
  };

  const handleRemoveUser = (userId) => {
    setSelectedUsers((prev) => prev.filter((user) => user.id !== userId));
  };

  // تابع برای مدیریت انتخاب "انتخاب همه" برای هر گروه permissions
  const handleSelectAll = (groupName, groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);
    const isAllSelected = allPermissionNames.every((permName) =>
      selectedPermissions.includes(permName),
    );

    if (isAllSelected) {
      // اگر همه انتخاب شده‌اند، همه را از انتخاب خارج کن
      setSelectedPermissions((prev) =>
        prev.filter((perm) => !allPermissionNames.includes(perm)),
      );
    } else {
      // اگر همه انتخاب نشده‌اند، همه را انتخاب کن
      setSelectedPermissions((prev) => {
        const filtered = prev.filter(
          (perm) => !allPermissionNames.includes(perm),
        );
        return [...filtered, ...allPermissionNames];
      });
    }
  };

  // تابع برای مدیریت انتخاب تک permission (رفتار radio با قابلیت لغو انتخاب)
  const handleSinglePermission = (permissionName, groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);

    // بررسی اینکه آیا این permission قبلاً انتخاب شده است یا نه
    const isCurrentlySelected = selectedPermissions.includes(permissionName);

    // ابتدا همه permissions این گروه را از انتخاب خارج کن
    const filteredPermissions = selectedPermissions.filter(
      (perm) => !allPermissionNames.includes(perm),
    );

    if (isCurrentlySelected) {
      // اگر قبلاً انتخاب شده بود، فقط آن را از انتخاب خارج کن (هیچ چیز اضافه نکن)
      setSelectedPermissions(filteredPermissions);
    } else {
      // اگر انتخاب نشده بود، آن را اضافه کن
      setSelectedPermissions([...filteredPermissions, permissionName]);
    }
  };

  // تابع برای بررسی اینکه آیا "انتخاب همه" برای یک گروه فعال است یا نه
  const isSelectAllChecked = (groupPermissions) => {
    const allPermissionNames = groupPermissions.map((perm) => perm.name);
    return allPermissionNames.every((permName) =>
      selectedPermissions.includes(permName),
    );
  };

  // تابع برای بررسی اینکه آیا یک permission خاص انتخاب شده است یا نه
  const isPermissionChecked = (permissionName) => {
    return selectedPermissions.includes(permissionName);
  };

  // تابع برای مدیریت "دسترسی کامل" - انتخاب/لغو انتخاب همه permissions
  const handleFullAccess = () => {
    if (!permissions) return;

    // تمام permissions موجود را استخراج کن
    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.name),
    );

    // بررسی اینکه آیا همه permissions انتخاب شده‌اند یا نه
    const areAllSelected = allPermissions.every((permName) =>
      selectedPermissions.includes(permName),
    );

    if (areAllSelected) {
      // اگر همه انتخاب شده‌اند، همه را از انتخاب خارج کن
      setSelectedPermissions([]);
    } else {
      // اگر همه انتخاب نشده‌اند، همه را انتخاب کن
      setSelectedPermissions(allPermissions);
    }
  };

  // تابع برای بررسی اینکه آیا "دسترسی کامل" فعال است یا نه
  const isFullAccessChecked = () => {
    if (!permissions) return false;

    const allPermissions = permissions.flatMap((group) =>
      group.permissions.map((perm) => perm.name),
    );

    return (
      allPermissions.length > 0 &&
      allPermissions.every((permName) => selectedPermissions.includes(permName))
    );
  };

  const onSubmit = (data) => {
    // تبدیل آرایه permissions انتخاب شده به رشته با کاما
    const permissionsString = selectedPermissions.join(",");

    const formData = {
      ...data,
      entity_id:
        MAX_SELECTED_USERS === 1
          ? selectedUsers[0]?.id
          : selectedUsers.map((user) => user.id),
      permissions: permissionsString, // اضافه کردن permissions به formData
    };
    console.log(formData);
  };

  const [usernameQuery] = useQueryStates({
    username: parseAsString,
  });

  // Get Entities Data
  const {
    data: _data,
    isLoading,
    error,
  } = api.Entities.list.useQuery({
    enabled: !!usernameQuery.username,
    variables: {
      query: {
        username: usernameQuery?.username,
      },
    },
  });
  const users = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  // Password Visibility
  const [isVisible, setIsVisible] = useState(false);
  const toggleVisibility = () => setIsVisible(!isVisible);

  // Get Permissions Data
  const {
    data: _permissions,
    isLoading: permissionsLoading,
    error: permissionsError,
  } = api.Permissions.list.useQuery();
  const permissions = useMemo(() => _permissions?.data, [_permissions]);

  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-col gap-6  rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <FormUploadAvatar
            name="image"
            control={control}
            classNames={{
              wrapper: "max-h-28 self-center md:self-auto max-w-28",
            }}
          />

          <p className="font-medium mt-2">اطلاعات سازمان</p>

          <div className="flex items-center flex-wrap md:flex-nowrap gap-4">
            <FormInput
              control={control}
              name="organizationName"
              type="text"
              inputProps={{
                classNames: {
                  base: "max-w-sm",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
                size: "lg",
                radius: "full",
                placeholder: "نام سازمان",
                startContent: <Edit className="size-6 text-primary" />,
              }}
            />
            <FormDatePicker
              control={control}
              name="date"
              isRange={true}
              size="lg"
              radius="full"
              startContent={
                <Icon className={"text-primary size-7"} name={"accept-note"} />
              }
              classNames={{
                base: "max-w-sm",
                inputWrapper:
                  "shadow-sm hover:!bg-background-100 border hover:border-foreground-200 transition-colors border-foreground-100",
                input: "text-sm ",
              }}
            />
          </div>

          <p className="font-medium mt-2">ویدیوی مرتبط با سازمان</p>

          <FormUpload control={control} name="video" />

          <p className="font-medium mt-2">توضیحات مرتبط با سازمان</p>
          <FormRichText
            control={control}
            name="description"
            enabledButtons={{
              image: false,
              link: false,
            }}
          />

          <FormSwitch
            control={control}
            className="ltr"
            name="active"
            label="وضعیت سازمان"
          />
        </div>

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <div className="flex w-full md:flex-nowrap flex-wrap items-center gap-4">
            <div className="relative w-full">
              <SearchFilter
                name="username"
                radius="full"
                placeholder="جستجوی بر اساس نام یا شماره موبایل"
                classNames={{
                  base: "w-full",
                  inputWrapper:
                    "!bg-background hover:!bg-background-100 min-h-11 shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
                }}
                onFocus={() => setSearchFocused(true)}
                onBlur={() => setTimeout(() => setSearchFocused(false), 200)}
                endContent={isLoading && <Spinner size="sm" />}
              />
              <AnimatePresence>
                {searchFocused && (
                  <motion.div
                    initial={{ opacity: 0, y: -20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeOut" }}
                    className="absolute border border-foreground-100 flex items-center justify-center flex-col rounded-2xl p-3 shadow-lg bg-background mt-1 w-full max-h-64 z-10"
                  >
                    <ScrollShadow hideScrollBar className="h-full w-full">
                      <ul className="space-y-2">
                        {!isLoading && !users?.data && (
                          <p className="text-center text-sm">
                            نتیجه‌ای یافت نشد
                          </p>
                        )}
                        {isLoading &&
                          !users?.data &&
                          [1, 2, 3].map((index) => (
                            <motion.li
                              key={index}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 0.1 }}
                              className="cursor-pointer hover:bg-foreground-100/50 rounded-lg px-2 py-1.5"
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex gap-2 items-center">
                                  <Skeleton className="flex-shrink-0 rounded-full size-10" />
                                  <div className="flex flex-col gap-1">
                                    <Skeleton className="h-3 w-24 rounded-lg" />
                                    <Skeleton className="h-3 w-16 rounded-lg" />
                                  </div>
                                </div>
                                <Skeleton className="h-8 w-16 rounded-full" />
                              </div>
                            </motion.li>
                          ))}
                        {users &&
                          !isLoading &&
                          users?.data?.map((item) => (
                            <motion.li
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 0.1 }}
                              key={item.id}
                              onClick={() => handleSelectUser(item)}
                              className="cursor-pointer hover:bg-foreground-100/50 rounded-lg px-2 py-1.5"
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex gap-2 items-center">
                                  <Avatar
                                    alt={item.fullname}
                                    className="flex-shrink-0"
                                    src={item.avatar}
                                  />
                                  <div className="flex flex-col">
                                    <span className="text-small ">
                                      {item.fullname}
                                    </span>
                                    <span className="text-tiny text-default-400">
                                      {item.mobile}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </motion.li>
                          ))}
                      </ul>
                    </ScrollShadow>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <Button
              type="button"
              color="primary"
              radius="full"
              className="px-6 shrink-0"
              onPress={onOpen}
              startContent={<Add className="size-6" />}
            >
              ایجاد ادمین جدید
            </Button>
          </div>

          {/* Create Modal */}
          <Modal
            size="lg"
            onClose={() => {
              createAdminReset();
            }}
            isOpen={isOpen}
            placement="center"
            className="mx-4"
            onOpenChange={onOpenChange}
          >
            <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
              {() => (
                <>
                  <ModalHeader className="flex flex-col gap-1" />
                  <ModalBody className="gap flex flex-col items-center">
                    <p className="text-lg font-semibold">ثبت درخواست</p>

                    <FormInput
                      control={createAdminControl}
                      name="first_name"
                      inputProps={{
                        classNames: {
                          base: "mt-4",
                          input: "text-sm",
                          inputWrapper:
                            "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                        },
                        size: "lg",
                        radius: "full",
                        placeholder: "نام",
                        startContent: (
                          <UserSquare className="size-6 text-primary" />
                        ),
                      }}
                    />
                    <FormInput
                      control={createAdminControl}
                      name="last_name"
                      inputProps={{
                        classNames: {
                          base: "mt-4",
                          input: "text-sm",
                          inputWrapper:
                            "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                        },
                        size: "lg",
                        radius: "full",
                        placeholder: "نام خانوادگی",
                        startContent: (
                          <UserSquare className="size-6 text-primary" />
                        ),
                      }}
                    />
                    <FormInput
                      control={createAdminControl}
                      name="mobile"
                      inputProps={{
                        classNames: {
                          base: "mt-4",
                          input: "text-sm",
                          inputWrapper:
                            "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                        },
                        size: "lg",
                        radius: "full",
                        placeholder: "موبایل",
                        startContent: <Call className="size-6 text-primary" />,
                      }}
                    />
                    <FormInput
                      control={createAdminControl}
                      name="password"
                      inputProps={{
                        type: isVisible ? "text" : "password",
                        classNames: {
                          base: "mt-4",
                          input: "text-sm",
                          inputWrapper:
                            "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                        },
                        size: "lg",
                        radius: "full",
                        placeholder: "رمز خود را وارد کنید",
                        startContent: <Lock1 className="size-6 text-primary" />,
                        endContent: (
                          <button
                            className="focus:outline-none"
                            type="button"
                            onClick={toggleVisibility}
                          >
                            {isVisible ? (
                              <EyeSlash className="pointer-events-none size-5 text-foreground-400" />
                            ) : (
                              <Eye className="pointer-events-none size-5 text-foreground-400" />
                            )}
                          </button>
                        ),
                      }}
                    />
                  </ModalBody>
                  <ModalFooter>
                    <Button
                      color="primary"
                      radius="full"
                      fullWidth
                      size="lg"
                      className="text-base font-medium"
                      type="submit"
                      onPress={createAdminSubmit(mutate)}
                    >
                      تائید
                    </Button>
                  </ModalFooter>
                </>
              )}
            </ModalContent>
          </Modal>

          {selectedUsers.length > 0 && (
            <ul className="space-y-4">
              {selectedUsers.map((user) => (
                <li
                  key={user.id}
                  className="flex items-center rounded-3xl gap-4 p-6 border border-foreground-100 shadow-sm"
                >
                  <Avatar
                    alt={user.fullname}
                    className="flex-shrink-0"
                    src={user.avatar}
                    size="lg"
                  />

                  <div className="flex items-center font-medium gap-2">
                    <p className="text-foreground-400">نام و نام خانوادگی: </p>
                    <p>{user.fullname}</p>
                  </div>

                  <div className="flex items-center font-medium gap-2">
                    <p className="text-foreground-400">شماره تماس: </p>
                    <p>{user.mobile}</p>
                  </div>

                  <Button
                    className="ms-auto"
                    isIconOnly
                    radius="full"
                    color="danger"
                    variant="light"
                    onPress={() => handleRemoveUser(user.id)}
                  >
                    <Trash />
                  </Button>
                </li>
              ))}
            </ul>
          )}

          <p className="font-medium mt-2">نقش</p>

          <div className="flex items-end md:flex-nowrap flex-wrap gap-3">
            <FormInput
              control={control}
              name="rolePersianName"
              inputProps={{
                placeholder: "عنوان نقش",

                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
            <FormInput
              control={control}
              name="roleLatinName"
              inputProps={{
                placeholder: "نام لاتین",

                radius: "full",
                size: "lg",
                classNames: {
                  base: "md:max-w-xs",
                  inputWrapper:
                    "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  input: "text-sm",
                },
              }}
            />
          </div>

          <div className="flex flex-col gap-4 ">
            <p className="font-medium mt-2">دسترسی‌ها</p>
            {/* چک‌باکس "دسترسی کامل" - زمانی که کلیک شود همه permissions در تمام گروه‌ها را انتخاب/لغو انتخاب می‌کند */}
            <Checkbox
              value={"all"}
              key={"all"}
              size="lg"
              radius="full"
              color="primary"
              isSelected={isFullAccessChecked()}
              onChange={handleFullAccess}
              classNames={{
                label: "text-sm font-medium text-inherit",
                base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
              }}
            >
              دسترسی کامل
            </Checkbox>

            {permissions?.map((item) => {
              return (
                <Fragment key={item?.name}>
                  <p className="font-medium mt-2">{item?.display_name}</p>
                  <div className="flex items-center gap-3">
                    {/* چک‌باکس "انتخاب همه" - زمانی که کلیک شود همه permissions این گروه را انتخاب/لغو انتخاب می‌کند */}
                    <Checkbox
                      value={item?.name}
                      key={item?.name}
                      size="lg"
                      radius="full"
                      color="primary"
                      isSelected={isSelectAllChecked(item?.permissions)}
                      onChange={() =>
                        handleSelectAll(item?.name, item?.permissions)
                      }
                      classNames={{
                        label: "text-sm font-medium text-inherit",
                        base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                      }}
                    >
                      انتخاب همه
                    </Checkbox>
                    {/* چک‌باکس‌های تک permission - رفتار radio دارند (فقط یکی در هر گروه قابل انتخاب) */}
                    {item?.permissions?.map((perm) => {
                      return (
                        <Checkbox
                          value={perm?.name}
                          key={perm?.name}
                          size="lg"
                          radius="full"
                          color="primary"
                          isSelected={isPermissionChecked(perm?.name)}
                          onChange={() =>
                            handleSinglePermission(
                              perm?.name,
                              item?.permissions,
                            )
                          }
                          classNames={{
                            label: "text-sm font-medium text-inherit",
                            base: "inline-flex bg-background m-0 px-4 text-foreground-500 data-[selected=true]:text-primary data-[selected=true]:border-primary data-[selected=true]:border-2 border border-foreground-100 rounded-full",
                          }}
                        >
                          {perm?.display_name}
                        </Checkbox>
                      );
                    })}
                  </div>
                </Fragment>
              );
            })}
          </div>

          <div className="flex gap-3 justify-end items-center">
            <Button
              type="submit"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              انصراف
            </Button>
            <Button
              type="submit"
              color="primary"
              radius="full"
              className="md:max-w-52"
              fullWidth
              size="lg"
            >
              ثبت سازمان
            </Button>
          </div>
        </div>
      </Form>
    </PageWrapper>
  );
};

export default CreateOrganizationsPage;
