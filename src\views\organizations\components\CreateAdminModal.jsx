import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@heroui/react";
import { Call, Eye, EyeSlash, Lock1, UserSquare } from "iconsax-reactjs";
import { useState } from "react";
import FormInput from "../../../components/form/FormInput";

const CreateAdminModal = ({
  isOpen,
  onOpenChange,
  onClose,
  control,
  onSubmit,
  onReset,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const toggleVisibility = () => setIsVisible(!isVisible);

  return (
    <Modal
      size="lg"
      onClose={() => {
        onReset();
      }}
      isOpen={isOpen}
      placement="center"
      className="mx-4"
      onOpenChange={onOpenChange}
    >
      <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1" />
            <ModalBody className="gap flex flex-col items-center">
              <p className="text-lg font-semibold">ثبت درخواست</p>

              <FormInput
                control={control}
                name="first_name"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "نام",
                  startContent: <UserSquare className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="last_name"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "نام خانوادگی",
                  startContent: <UserSquare className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="mobile"
                inputProps={{
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "موبایل",
                  startContent: <Call className="size-6 text-primary" />,
                }}
              />
              <FormInput
                control={control}
                name="password"
                inputProps={{
                  type: isVisible ? "text" : "password",
                  classNames: {
                    base: "mt-4",
                    input: "text-sm",
                    inputWrapper:
                      "bg-background hover:border-foreground-200 transition-colors hover:!bg-background-100 shadow-sm border border-foreground-100",
                  },
                  size: "lg",
                  radius: "full",
                  placeholder: "رمز خود را وارد کنید",
                  startContent: <Lock1 className="size-6 text-primary" />,
                  endContent: (
                    <button
                      className="focus:outline-none"
                      type="button"
                      onClick={toggleVisibility}
                    >
                      {isVisible ? (
                        <EyeSlash className="pointer-events-none size-5 text-foreground-400" />
                      ) : (
                        <Eye className="pointer-events-none size-5 text-foreground-400" />
                      )}
                    </button>
                  ),
                }}
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                radius="full"
                fullWidth
                size="lg"
                className="text-base font-medium"
                type="submit"
                onPress={onSubmit}
              >
                تائید
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default CreateAdminModal;
